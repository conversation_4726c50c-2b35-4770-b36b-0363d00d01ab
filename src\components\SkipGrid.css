/* SkipGrid.css - Hexagonal Layout */
.skip-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Hexagonal honeycomb pattern */
.skip-grid .skip-card:nth-child(even) {
  margin-top: 4rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skip-grid {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    padding: 2rem 1rem;
  }

  .skip-grid .skip-card:nth-child(even) {
    margin-top: 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .skip-grid {
    gap: 1.5rem;
  }

  .skip-grid .skip-card:nth-child(3n) {
    margin-top: 4rem;
  }
}

@media (min-width: 1025px) {
  .skip-grid {
    gap: 2rem;
  }

  .skip-grid .skip-card:nth-child(4n-1),
  .skip-grid .skip-card:nth-child(4n) {
    margin-top: 4rem;
  }
}
