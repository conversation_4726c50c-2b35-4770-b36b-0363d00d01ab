/* SkipGrid.css */
.skip-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  padding: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skip-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0.5rem 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .skip-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .skip-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
