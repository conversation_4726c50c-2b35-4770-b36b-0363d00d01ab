/* Industrial-Neon Unique Grid Layout */
.skip-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 3rem 2rem;
  padding: 3rem 0;
  perspective: 1000px;
  position: relative;
}

.skip-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 49%, rgba(255, 107, 53, 0.1) 50%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, rgba(34, 197, 94, 0.1) 50%, transparent 51%);
  background-size: 60px 60px;
  pointer-events: none;
  opacity: 0.3;
  z-index: -1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skip-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0.5rem 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .skip-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .skip-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
