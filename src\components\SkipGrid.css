/* V1 Professional Grid Layout - Perfect Screen Fit */
.skip-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 0;
  width: 100%;
  flex: 1;
}

/* Perfect Responsive Grid for All Screen Sizes */
@media (max-width: 640px) {
  .skip-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .skip-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) and (max-width: 1400px) {
  .skip-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1401px) {
  .skip-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}
