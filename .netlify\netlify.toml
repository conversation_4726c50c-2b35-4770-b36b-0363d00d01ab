headersOrigin = "config"
redirectsOrigin = "config"
plugins = []

[functions]

[functions."*"]

[build]
publish = "C:\\Users\\<USER>\\OneDrive\\Desktop\\-https-wewantwaste.co.uk-CodeChallenge-\\dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"

[build.environment]
NODE_VERSION = "18"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/assets/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.js"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "*.css"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]