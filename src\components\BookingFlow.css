/* BookingFlow.css - Professional Skip Hire Booking */
.booking-flow {
  min-height: 100vh;
  background: rgba(15, 20, 25, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  overflow-y: auto;
}

.booking-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

/* Progress Header */
.booking-header {
  margin-bottom: 3rem;
}

.step-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.step-progress::before {
  content: '';
  position: absolute;
  top: 1.5rem;
  left: 2rem;
  right: 2rem;
  height: 2px;
  background: rgba(255, 107, 53, 0.3);
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.step-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.progress-step.pending .step-icon {
  background: rgba(107, 114, 128, 0.3);
  color: rgba(255, 255, 255, 0.6);
  border: 2px solid rgba(107, 114, 128, 0.5);
}

.progress-step.active .step-icon {
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
  color: white;
  border: 2px solid #ff6b35;
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
  animation: activeStepPulse 2s ease-in-out infinite;
}

.progress-step.completed .step-icon {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: 2px solid #22c55e;
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
}

@keyframes activeStepPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
  }
}

.step-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.progress-step.active .step-title {
  color: #ff6b35;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.progress-step.completed .step-title {
  color: #22c55e;
}

/* Selected Skip Summary */
.selected-skip-summary {
  background: rgba(34, 197, 94, 0.1);
  border: 2px solid rgba(34, 197, 94, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 3rem;
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.summary-badge {
  position: absolute;
  top: -1rem;
  left: 2rem;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 2rem;
  font-weight: 800;
  font-size: 0.875rem;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.skip-details h3 {
  color: #22c55e;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

.skip-details p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
}

.price-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(34, 197, 94, 0.3);
}

.price {
  font-size: 2rem;
  font-weight: 900;
  color: #ff6b35;
  text-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
}

.hire-period {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
}

/* Step Content */
.step-content {
  background: rgba(15, 20, 25, 0.8);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-container {
    padding: 1rem;
  }
  
  .step-progress {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step-progress::before {
    display: none;
  }
  
  .progress-step {
    flex-direction: row;
    width: 100%;
    justify-content: flex-start;
    gap: 1rem;
  }
  
  .step-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .selected-skip-summary {
    padding: 1.5rem;
  }
  
  .price-display {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .step-content {
    padding: 1.5rem;
  }
}
