/* V2 Neon Header Design */
.header {
  background: rgba(10, 10, 15, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow:
    0 4px 30px rgba(0, 255, 255, 0.2),
    0 0 50px rgba(0, 255, 255, 0.1);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(255, 0, 255, 0.1) 50%,
    rgba(0, 255, 127, 0.1) 100%);
  opacity: 0.5;
  z-index: -1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo {
  font-size: 2rem;
  font-weight: 900;
  color: #00ffff;
  margin: 0;
  text-decoration: none;
  text-shadow:
    0 0 10px #00ffff,
    0 0 20px #00ffff,
    0 0 30px #00ffff;
  animation: logoGlow 3s ease-in-out infinite alternate;
  letter-spacing: 2px;
  text-transform: uppercase;
}

@keyframes logoGlow {
  from {
    text-shadow:
      0 0 10px #00ffff,
      0 0 20px #00ffff,
      0 0 30px #00ffff;
  }
  to {
    text-shadow:
      0 0 15px #00ffff,
      0 0 25px #00ffff,
      0 0 35px #00ffff,
      0 0 40px #00ffff;
  }
}

.tagline {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.controls {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.control-group label {
  font-size: 0.875rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-select {
  padding: 0.75rem 1.25rem;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 1rem;
  background: rgba(10, 10, 15, 0.8);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-weight: 600;
}

.control-select:hover {
  border-color: #00ffff;
  background: rgba(0, 255, 255, 0.1);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.control-select:focus {
  outline: none;
  border-color: #ff00ff;
  background: rgba(255, 0, 255, 0.1);
  box-shadow:
    0 0 20px rgba(255, 0, 255, 0.4),
    0 0 0 3px rgba(255, 0, 255, 0.2);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .logo-section {
    align-items: center;
    text-align: center;
  }
  
  .controls {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
  
  .control-group {
    width: 100%;
  }
  
  .control-select {
    width: 100%;
    min-width: unset;
  }
}
