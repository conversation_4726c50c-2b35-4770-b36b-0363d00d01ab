/* V1 Professional Header Design - Perfect Screen Fit */
.header {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo {
  font-size: 1.875rem;
  font-weight: 700;
  color: #ff6b35;
  margin: 0;
  text-decoration: none;
  letter-spacing: 1px;
}

.tagline {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.controls {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.control-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  background: #ffffff;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  font-weight: 500;
}

.control-select:hover {
  border-color: #ff6b35;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
}

.control-select:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

/* Perfect Mobile Responsive Design */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 1.25rem;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0.75rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 0.75rem;
    padding: 0 1rem;
  }

  .logo-section {
    align-items: center;
    text-align: center;
  }

  .controls {
    flex-direction: row;
    gap: 1rem;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-group {
    flex: 1;
    min-width: 140px;
  }

  .control-select {
    width: 100%;
    min-width: unset;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 0.75rem;
  }

  .controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .control-group {
    width: 100%;
  }
}
