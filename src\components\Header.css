/* V2 Industrial-Neon Header Design */
.header {
  background: linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(30, 35, 40, 0.9) 100%);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  border-bottom: 3px solid rgba(255, 107, 53, 0.4);
  padding: 2rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow:
    0 6px 40px rgba(255, 107, 53, 0.2),
    0 0 60px rgba(255, 107, 53, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 1rem), calc(100% - 2rem) 100%, 2rem 100%, 0 calc(100% - 1rem));
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(255, 0, 255, 0.1) 50%,
    rgba(0, 255, 127, 0.1) 100%);
  opacity: 0.5;
  z-index: -1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ff6b35;
  margin: 0;
  text-decoration: none;
  text-shadow:
    0 0 10px #ff6b35,
    0 0 20px #ff6b35,
    0 0 30px #ff6b35;
  animation: logoGlow 4s ease-in-out infinite alternate;
  letter-spacing: 3px;
  text-transform: uppercase;
  background: linear-gradient(135deg, #ff6b35 0%, #22c55e 50%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes logoGlow {
  from {
    text-shadow:
      0 0 10px #ff6b35,
      0 0 20px #ff6b35,
      0 0 30px #ff6b35;
  }
  to {
    text-shadow:
      0 0 15px #ff6b35,
      0 0 25px #ff6b35,
      0 0 35px #ff6b35,
      0 0 40px #ff6b35;
  }
}

.tagline {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.controls {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.control-group label {
  font-size: 0.875rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-select {
  padding: 0.75rem 1.25rem;
  border: 2px solid rgba(255, 107, 53, 0.3);
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, 100% 0.5rem, 100% 100%, 0.5rem 100%, 0 calc(100% - 0.5rem));
  background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(30, 35, 40, 0.8) 100%);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.control-select:hover {
  border-color: #ff6b35;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
  box-shadow:
    0 0 25px rgba(255, 107, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.control-select:focus {
  outline: none;
  border-color: #22c55e;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  box-shadow:
    0 0 25px rgba(34, 197, 94, 0.4),
    0 0 0 3px rgba(34, 197, 94, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .logo-section {
    align-items: center;
    text-align: center;
  }
  
  .controls {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
  
  .control-group {
    width: 100%;
  }
  
  .control-select {
    width: 100%;
    min-width: unset;
  }
}
