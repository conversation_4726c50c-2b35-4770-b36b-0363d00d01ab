/* SkipCard.css */
.skip-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.skip-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.skip-card.selected {
  border-color: #4f46e5;
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.2);
}

.skip-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.skip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.skip-card:hover .skip-image img {
  transform: scale(1.05);
}

.skip-size-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #4f46e5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.skip-content {
  padding: 1.5rem;
}

.skip-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.skip-period {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.skip-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.feature-badge.road-allowed {
  background: #dcfce7;
  color: #166534;
}

.feature-badge.heavy-waste {
  background: #fef3c7;
  color: #92400e;
}

.skip-pricing {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.price-breakdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.price-before-vat {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.vat-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.total-label {
  font-weight: 600;
  color: #374151;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4f46e5;
}

.select-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-button:hover {
  background: #4338ca;
  transform: translateY(-1px);
}

.skip-card.selected .select-button {
  background: #10b981;
}

.skip-card.selected .select-button:hover {
  background: #059669;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .skip-content {
    padding: 1rem;
  }
  
  .skip-image {
    height: 180px;
  }
  
  .skip-features {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .feature-badge {
    align-self: flex-start;
  }
}
