/* SkipCard.css */
.skip-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 3px solid transparent;
  position: relative;
}

.skip-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #ff6b35;
}

.skip-card.selected {
  border-color: #ff6b35;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
  background: linear-gradient(135deg, #fff 0%, #f0f7ff 100%);
}

.skip-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.skip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.skip-card:hover .skip-image img {
  transform: scale(1.05);
}

.skip-size-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ff6b35;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 700;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.skip-content {
  padding: 1.5rem;
}

.skip-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.skip-period {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.skip-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.feature-badge.road-allowed {
  background: #dbeafe;
  color: #1E3A8A;
  border: 1px solid #93c5fd;
}

.feature-badge.heavy-waste {
  background: #fff2e6;
  color: #cc6600;
  border: 1px solid #ffcc99;
}

.feature-badge.not-road-allowed {
  background: #ffe6e6;
  color: #cc0000;
  border: 1px solid #ffb3b3;
}

.skip-pricing {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.price-breakdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.price-before-vat {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.vat-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.total-label {
  font-weight: 600;
  color: #374151;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff6b35;
}

.select-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: #ff6b35;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.select-button:hover {
  background: #e55a2b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.select-button.selected {
  background: #28a745;
  color: white;
}

.select-button.selected:hover {
  background: #218838;
}

/* Selection Details */
.selection-details {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #ff6b35 0%, #f55a2a 100%);
  border-radius: 0.5rem;
  color: white;
  animation: slideDown 0.3s ease-out;
}

.next-steps h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 700;
  color: white;
}

.next-steps ul {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
}

.next-steps li {
  padding: 0.25rem 0;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.booking-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.book-now-btn, .call-btn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.book-now-btn {
  background: white;
  color: #ff6b35;
}

.book-now-btn:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
}

.call-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.call-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .skip-content {
    padding: 1rem;
  }

  .skip-image {
    height: 180px;
  }

  .skip-features {
    flex-direction: column;
    gap: 0.25rem;
  }

  .feature-badge {
    align-self: flex-start;
  }

  .booking-actions {
    flex-direction: column;
  }

  .book-now-btn, .call-btn {
    flex: none;
  }
}
