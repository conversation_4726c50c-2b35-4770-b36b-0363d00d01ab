/* SkipCard.css - V2 Hexagonal Design */
.skip-card {
  background: white;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  margin: 2rem 1rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.skip-card::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #ff6b35, #1E3A8A, #3B82F6, #ff6b35);
  border-radius: 1rem;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skip-card:hover::before {
  opacity: 1;
}

.skip-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.skip-card.selected {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 30px rgba(255, 107, 53, 0.4);
}

.skip-card.selected::before {
  opacity: 1;
  background: linear-gradient(45deg, #ff6b35, #ff6b35, #ff6b35, #ff6b35);
}

.skip-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.skip-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(30, 58, 138, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 50% 50%, rgba(30, 58, 138, 0.1) 0%, transparent 30%);
  background-size: 80px 80px, 60px 60px, 70px 70px, 90px 90px, 50px 50px;
  background-position: 0 0, 40px 40px, 80px 0, 120px 40px, 25px 25px;
  z-index: 1;
  animation: hexagonFloat 15s ease-in-out infinite;
}

@keyframes hexagonFloat {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.7;
  }
  33% {
    transform: rotate(120deg) scale(1.1);
    opacity: 0.9;
  }
  66% {
    transform: rotate(240deg) scale(0.9);
    opacity: 0.8;
  }
}

.skip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 2;
  mix-blend-mode: multiply;
}

.skip-card:hover .skip-image img {
  transform: scale(1.05);
}

.skip-size-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ff6b35;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 700;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.skip-content {
  padding: 2rem 1.5rem;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.skip-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(30deg, transparent 40%, rgba(255, 107, 53, 0.03) 50%, transparent 60%),
    linear-gradient(150deg, transparent 40%, rgba(30, 58, 138, 0.03) 50%, transparent 60%);
  background-size: 120px 120px, 80px 80px;
  background-position: 0 0, 60px 40px;
  z-index: 0;
}

.skip-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  position: relative;
  z-index: 1;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.skip-period {
  color: #6b7280;
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  position: relative;
  z-index: 1;
  text-align: center;
  font-weight: 600;
}

.skip-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.feature-badge.road-allowed {
  background: #dbeafe;
  color: #1E3A8A;
  border: 1px solid #93c5fd;
}

.feature-badge.heavy-waste {
  background: #fff2e6;
  color: #cc6600;
  border: 1px solid #ffcc99;
}

.feature-badge.not-road-allowed {
  background: #ffe6e6;
  color: #cc0000;
  border: 1px solid #ffb3b3;
}

.skip-pricing {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f9fafb 0%, #e2e8f0 100%);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.skip-pricing::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff6b35, #1E3A8A, #3B82F6, #ff6b35);
  border-radius: 1rem;
  z-index: -1;
  opacity: 0.3;
}

.price-breakdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.price-before-vat {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.vat-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.total-label {
  font-weight: 600;
  color: #374151;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff6b35;
}

.select-button {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
  color: white;
  border: none;
  border-radius: 2rem;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.select-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.select-button:hover::before {
  left: 100%;
}

.select-button:hover {
  background: linear-gradient(135deg, #e55a2b 0%, #d14d20 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.select-button.selected {
  background: linear-gradient(135deg, #28a745 0%, #218838 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.select-button.selected:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Selection Details */
.selection-details {
  margin-top: 1rem;
  padding: 0;
  background: #1a1a1a;
  border-radius: 0.5rem;
  color: white;
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

/* Process Steps */
.process-steps {
  padding: 1.5rem;
  background: #1a1a1a;
}

.step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #333;
}

.step:last-child {
  border-bottom: none;
}

.step-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.step.completed .step-icon {
  background: #28a745;
  color: white;
}

.step.next .step-icon {
  background: #ff6b35;
  color: white;
}

.step.pending .step-icon {
  background: #6c757d;
  color: white;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.step-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
}

.step-detail {
  font-size: 0.75rem;
  color: #adb5bd;
}

.step.completed .step-title {
  color: #28a745;
}

.step.next .step-title {
  color: #ff6b35;
}

/* Selection Summary */
.selection-summary {
  padding: 1rem 1.5rem;
  background: #2d2d2d;
  border-top: 1px solid #333;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.summary-row.hire-period {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 600;
  color: white;
  font-size: 1rem;
}

.summary-value {
  font-weight: 700;
  color: #ff6b35;
  font-size: 1.25rem;
}

.summary-detail {
  font-size: 0.875rem;
  color: #adb5bd;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0;
  background: #2d2d2d;
}

.back-btn, .continue-btn {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border: none;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn {
  background: #6c757d;
  color: white;
}

.back-btn:hover {
  background: #5a6268;
}

.continue-btn {
  background: #007bff;
  color: white;
}

.continue-btn:hover {
  background: #0056b3;
}

/* Disclaimer */
.disclaimer {
  padding: 1rem 1.5rem;
  background: #1a1a1a;
  border-top: 1px solid #333;
}

.disclaimer p {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
  text-align: center;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .skip-content {
    padding: 1rem;
  }

  .skip-image {
    height: 180px;
  }

  .skip-features {
    flex-direction: column;
    gap: 0.25rem;
  }

  .feature-badge {
    align-self: flex-start;
  }

  .booking-actions {
    flex-direction: column;
  }

  .book-now-btn, .call-btn {
    flex: none;
  }
}
