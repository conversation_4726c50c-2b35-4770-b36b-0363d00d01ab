/* V2 Neon SkipCard Design */
.skip-card {
  background: rgba(10, 10, 15, 0.8);
  border-radius: 1.5rem;
  overflow: hidden;
  border: 2px solid rgba(0, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.skip-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(255, 0, 255, 0.1) 50%,
    rgba(0, 255, 127, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.skip-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: #ff6b35;
  box-shadow:
    0 20px 40px rgba(255, 107, 53, 0.2),
    0 0 20px rgba(255, 107, 53, 0.3),
    inset 0 0 20px rgba(255, 107, 53, 0.1);
}

.skip-card:hover::before {
  opacity: 1;
}

.skip-card.selected {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  box-shadow:
    0 20px 40px rgba(34, 197, 94, 0.3),
    0 0 30px rgba(34, 197, 94, 0.4),
    inset 0 0 30px rgba(34, 197, 94, 0.2);
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% {
    box-shadow:
      0 20px 40px rgba(34, 197, 94, 0.3),
      0 0 30px rgba(34, 197, 94, 0.4),
      inset 0 0 30px rgba(34, 197, 94, 0.2);
  }
  50% {
    box-shadow:
      0 25px 50px rgba(34, 197, 94, 0.4),
      0 0 40px rgba(34, 197, 94, 0.5),
      inset 0 0 40px rgba(34, 197, 94, 0.3);
  }
}

.skip-image {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(255, 0, 255, 0.1) 100%);
}

.skip-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
  filter: brightness(0.8) contrast(1.2);
}

.skip-card:hover .skip-image img {
  transform: scale(1.1);
  filter: brightness(1) contrast(1.3);
}

.skip-size-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 2rem;
  font-weight: 800;
  font-size: 0.875rem;
  box-shadow:
    0 4px 15px rgba(255, 107, 53, 0.4),
    0 0 20px rgba(255, 107, 53, 0.3);
  text-shadow: none;
  border: 1px solid rgba(255, 107, 53, 0.5);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% {
    box-shadow:
      0 4px 15px rgba(255, 107, 53, 0.4),
      0 0 20px rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow:
      0 6px 20px rgba(255, 107, 53, 0.5),
      0 0 30px rgba(255, 107, 53, 0.4);
  }
}

.skip-content {
  padding: 2rem 1.5rem;
  background: linear-gradient(135deg,
    rgba(10, 10, 15, 0.9) 0%,
    rgba(26, 10, 46, 0.9) 100%);
  position: relative;
}

.skip-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    #00ffff 50%,
    transparent 100%);
}

.skip-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 0.75rem 0;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  text-align: center;
}

.skip-period {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  text-align: center;
  font-weight: 600;
}

.skip-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.feature-badge.road-allowed {
  background: #dbeafe;
  color: #1E3A8A;
  border: 1px solid #93c5fd;
}

.feature-badge.heavy-waste {
  background: #fff2e6;
  color: #cc6600;
  border: 1px solid #ffcc99;
}

.feature-badge.not-road-allowed {
  background: #ffe6e6;
  color: #cc0000;
  border: 1px solid #ffb3b3;
}

.skip-pricing {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 107, 53, 0.05);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 107, 53, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.skip-pricing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 107, 53, 0.1) 0%,
    rgba(34, 197, 94, 0.1) 50%,
    rgba(59, 130, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skip-card:hover .skip-pricing::before {
  opacity: 1;
}

.price-breakdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.price-before-vat {
  font-size: 1.25rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

.vat-info {
  font-size: 0.875rem;
  color: rgba(255, 107, 53, 0.8);
  font-weight: 600;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 107, 53, 0.3);
}

.total-label {
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

.total-amount {
  font-size: 2rem;
  font-weight: 900;
  color: #22c55e;
  text-shadow: 0 0 15px rgba(34, 197, 94, 0.6);
  animation: priceGlow 2s ease-in-out infinite alternate;
}

@keyframes priceGlow {
  from {
    text-shadow: 0 0 15px rgba(34, 197, 94, 0.6);
  }
  to {
    text-shadow: 0 0 25px rgba(34, 197, 94, 0.8);
  }
}

.select-button {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
  color: white;
  border: none;
  border-radius: 2rem;
  font-weight: 800;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(255, 107, 53, 0.3),
    0 0 20px rgba(255, 107, 53, 0.2);
}

.select-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.select-button:hover::before {
  left: 100%;
}

.select-button:hover {
  background: linear-gradient(135deg, #e55a2b 0%, #d14d1f 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(255, 107, 53, 0.4),
    0 0 30px rgba(255, 107, 53, 0.3);
}

.select-button.selected {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow:
    0 8px 25px rgba(34, 197, 94, 0.4),
    0 0 30px rgba(34, 197, 94, 0.3);
  animation: selectedButtonPulse 2s ease-in-out infinite;
}

@keyframes selectedButtonPulse {
  0%, 100% {
    box-shadow:
      0 8px 25px rgba(34, 197, 94, 0.4),
      0 0 30px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow:
      0 12px 35px rgba(34, 197, 94, 0.5),
      0 0 40px rgba(34, 197, 94, 0.4);
  }
}

.select-button.selected:hover {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  transform: translateY(-3px) scale(1.02);
}

/* Selection Details */
.selection-details {
  margin-top: 1rem;
  padding: 0;
  background: #1a1a1a;
  border-radius: 0.5rem;
  color: white;
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

/* Process Steps */
.process-steps {
  padding: 1.5rem;
  background: #1a1a1a;
}

.step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #333;
}

.step:last-child {
  border-bottom: none;
}

.step-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.step.completed .step-icon {
  background: #28a745;
  color: white;
}

.step.next .step-icon {
  background: #ff6b35;
  color: white;
}

.step.pending .step-icon {
  background: #6c757d;
  color: white;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.step-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
}

.step-detail {
  font-size: 0.75rem;
  color: #adb5bd;
}

.step.completed .step-title {
  color: #28a745;
}

.step.next .step-title {
  color: #ff6b35;
}

/* Selection Summary */
.selection-summary {
  padding: 1rem 1.5rem;
  background: #2d2d2d;
  border-top: 1px solid #333;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.summary-row.hire-period {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 600;
  color: white;
  font-size: 1rem;
}

.summary-value {
  font-weight: 700;
  color: #ff6b35;
  font-size: 1.25rem;
}

.summary-detail {
  font-size: 0.875rem;
  color: #adb5bd;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0;
  background: #2d2d2d;
}

.back-btn, .continue-btn {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border: none;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn {
  background: #6c757d;
  color: white;
}

.back-btn:hover {
  background: #5a6268;
}

.continue-btn {
  background: #007bff;
  color: white;
}

.continue-btn:hover {
  background: #0056b3;
}

/* Disclaimer */
.disclaimer {
  padding: 1rem 1.5rem;
  background: #1a1a1a;
  border-top: 1px solid #333;
}

.disclaimer p {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
  text-align: center;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .skip-content {
    padding: 1rem;
  }

  .skip-image {
    height: 180px;
  }

  .skip-features {
    flex-direction: column;
    gap: 0.25rem;
  }

  .feature-badge {
    align-self: flex-start;
  }

  .booking-actions {
    flex-direction: column;
  }

  .book-now-btn, .call-btn {
    flex: none;
  }
}
