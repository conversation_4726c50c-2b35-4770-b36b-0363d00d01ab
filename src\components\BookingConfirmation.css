/* BookingConfirmation.css - Success Page Styling */
.booking-confirmation {
  color: white;
}

.confirmation-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(34, 197, 94, 0.1);
  border: 2px solid rgba(34, 197, 94, 0.3);
  border-radius: 1.5rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.success-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 12px 35px rgba(34, 197, 94, 0.4);
  }
}

.confirmation-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #22c55e;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 0 15px rgba(34, 197, 94, 0.5);
}

.confirmation-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0 0 1.5rem 0;
}

.booking-reference {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.4);
  border-radius: 1rem;
  padding: 1rem 2rem;
  display: inline-block;
  font-size: 1.1rem;
}

.booking-reference strong {
  color: #22c55e;
  font-weight: 800;
}

.confirmation-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
}

.detail-section {
  background: rgba(15, 20, 25, 0.6);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 1rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.detail-section h3 {
  margin: 0 0 1.5rem 0;
  font-weight: 700;
  color: #ff6b35;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
}

.detail-item .value {
  color: white;
  font-weight: 700;
}

.address-info p {
  color: white;
  font-weight: 600;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.placement-info {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.placement-badge, .permit-badge {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.4);
  color: #3b82f6;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 700;
}

.permit-badge {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
  color: #ff6b35;
}

.payment-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 2px solid rgba(255, 107, 53, 0.3);
  margin-top: 1rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: #ff6b35;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 1px solid rgba(34, 197, 94, 0.3);
  margin-top: 1rem;
}

.payment-status {
  color: #22c55e;
  font-weight: 700;
}

.next-steps {
  background: rgba(15, 20, 25, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 3rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.next-steps h3 {
  margin: 0 0 2rem 0;
  font-weight: 700;
  color: #3b82f6;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.step-content {
  flex: 1;
}

.step-content strong {
  display: block;
  color: white;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

.confirmation-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .placement-info {
    flex-direction: column;
  }
  
  .confirmation-actions {
    flex-direction: column;
  }
  
  .step-item {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    align-self: center;
  }
}
