/* App.css - Main application styles */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #f55a2a 100%);
}

.main-content {
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .main-content {
    padding: 1rem 0;
  }
}
