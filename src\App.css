/* V1 Professional Skip Hire Design - Perfect Screen Fit */
.app {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow-x: hidden;
  position: relative;
}

/* Main Content */
.main-content {
  padding: 1rem 0 2rem 0;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem 0;
  flex-shrink: 0;
}

.page-header h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.75rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.page-header p {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.5;
}

/* Enhanced Responsive Design for Perfect Screen Fit */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 1.25rem;
  }
}

@media (max-width: 768px) {
  .app {
    min-height: 100vh;
  }

  .main-content {
    padding: 0.5rem 0 1.5rem 0;
    min-height: calc(100vh - 100px);
  }

  .container {
    padding: 0 1rem;
  }

  .page-header {
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .page-header {
    padding: 0.75rem 0;
    margin-bottom: 1rem;
  }
}

/* Ensure perfect viewport fit */
@media (max-height: 600px) {
  .main-content {
    padding: 0.5rem 0 1rem 0;
  }

  .page-header {
    padding: 0.5rem 0;
    margin-bottom: 1rem;
  }
}
