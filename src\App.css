/* V2 Professional Skip Hire Design */
.app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: #0f1419;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Professional Background Effects */
.neon-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.06) 0%, transparent 50%),
    linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #2d1b69 100%);
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Floating Particles and Effects */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 107, 53, 0.6), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(34, 197, 94, 0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(59, 130, 246, 0.6), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: particleFloat 15s ease-in-out infinite;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.cyber-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, transparent 79px, rgba(255, 107, 53, 0.03) 81px, rgba(255, 107, 53, 0.03) 82px, transparent 84px),
    linear-gradient(transparent 79px, rgba(34, 197, 94, 0.03) 81px, rgba(34, 197, 94, 0.03) 82px, transparent 84px);
  background-size: 84px 84px;
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 1;
  padding: 2rem 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 4rem;
  padding: 4rem 0;
}

.neon-title {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.neon-text {
  color: #ff6b35;
  text-shadow:
    0 0 5px #ff6b35,
    0 0 10px #ff6b35,
    0 0 15px #ff6b35,
    0 0 20px #ff6b35;
  animation: neonPulse 2s ease-in-out infinite alternate;
}

.neon-text-secondary {
  color: #22c55e;
  text-shadow:
    0 0 5px #22c55e,
    0 0 10px #22c55e,
    0 0 15px #22c55e,
    0 0 20px #22c55e;
  animation: neonPulse 2s ease-in-out infinite alternate-reverse;
}

.neon-text-accent {
  color: #3b82f6;
  text-shadow:
    0 0 5px #3b82f6,
    0 0 10px #3b82f6,
    0 0 15px #3b82f6;
  font-size: 2rem;
  animation: neonFlicker 3s ease-in-out infinite;
}

@keyframes neonPulse {
  from {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  to {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

@keyframes neonFlicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Stats Bar */
.stats-bar {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1rem 2rem;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #ff6b35;
  text-shadow: 0 0 10px #ff6b35;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .neon-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .neon-text-accent {
    font-size: 1.5rem;
  }

  .stats-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .stat-item {
    padding: 0.75rem 1.5rem;
  }

  .hero-section {
    padding: 2rem 0;
  }
}
