/* DateSelection.css - Professional Date Picker */
.date-selection {
  color: white;
}

.date-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.date-option {
  display: block;
  padding: 1.5rem;
  background: rgba(15, 20, 25, 0.6);
  border: 2px solid rgba(255, 107, 53, 0.2);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
}

.date-option:hover:not(.unavailable) {
  border-color: rgba(255, 107, 53, 0.4);
  background: rgba(255, 107, 53, 0.05);
  transform: translateY(-2px);
}

.date-option.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(107, 114, 128, 0.2);
  border-color: rgba(107, 114, 128, 0.3);
}

.date-option input[type="radio"] {
  display: none;
}

.date-option input[type="radio"]:checked + .date-content {
  color: #ff6b35;
}

.date-option input[type="radio"]:checked + .date-content::before {
  content: '✓';
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  background: #ff6b35;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
}

.date-option input[type="radio"]:checked {
  + .date-content {
    color: #ff6b35;
  }
}

.date-option:has(input[type="radio"]:checked) {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
}

.date-content {
  position: relative;
}

.date-label {
  font-weight: 700;
  font-size: 1rem;
}

.unavailable-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 700;
}

.time-slot-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.time-slot-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(15, 20, 25, 0.6);
  border: 2px solid rgba(255, 107, 53, 0.2);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.time-slot-option:hover {
  border-color: rgba(255, 107, 53, 0.4);
  background: rgba(255, 107, 53, 0.05);
}

.time-slot-option input[type="radio"] {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid rgba(255, 107, 53, 0.5);
  border-radius: 50%;
  appearance: none;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-slot-option input[type="radio"]:checked {
  border-color: #ff6b35;
  background: #ff6b35;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
}

.time-slot-option input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
}

.time-slot-option:has(input[type="radio"]:checked) {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
}

.slot-content {
  flex: 1;
}

.slot-content strong {
  display: block;
  color: white;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.slot-content small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.time-slot-option:has(input[type="radio"]:checked) .slot-content strong {
  color: #ff6b35;
}

.selection-summary {
  display: flex;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(34, 197, 94, 0.1);
  border: 2px solid rgba(34, 197, 94, 0.3);
  border-radius: 1rem;
  margin-top: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: slideIn 0.5s ease-out;
}

.summary-icon {
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-content h3 {
  margin: 0 0 1rem 0;
  font-weight: 700;
  color: #22c55e;
}

.summary-content p {
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 0.9);
}

.summary-content strong {
  color: white;
}

.step-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-primary-full {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  border: none;
  clip-path: polygon(0 0, calc(100% - 1rem) 0, 100% 1rem, 100% 100%, 1rem 100%, 0 calc(100% - 1rem));
  font-weight: 800;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
  color: white;
  box-shadow:
    0 0 0 2px rgba(255, 107, 53, 0.3),
    0 8px 25px rgba(255, 107, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-primary-full:hover:not(:disabled) {
  background: linear-gradient(135deg, #e55a2b 0%, #d14d1f 100%);
  box-shadow:
    0 0 0 3px rgba(255, 107, 53, 0.5),
    0 12px 35px rgba(255, 107, 53, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) scale(1.02);
}

.btn-primary-full:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .date-grid {
    grid-template-columns: 1fr;
  }

  .date-option, .time-slot-option {
    padding: 1rem;
  }

  .selection-summary {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .summary-icon {
    align-self: center;
  }

  .btn-primary-full {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
  }
}
