import './SkipCard.css'

const SkipCard = ({ skip, isSelected, onSelect }) => {
  const calculateTotalPrice = () => {
    const vatAmount = (skip.price_before_vat * skip.vat) / 100
    return skip.price_before_vat + vatAmount
  }

  const getSkipImage = (size) => {
    // Use real skip images from Supabase storage
    const baseUrl = 'https://yozbrydxdlcxghkphhtq.supabase.co/storage/v1/object/public/skips/skip-sizes/'
    const imageMap = {
      4: '4-yarder-skip.jpg',
      6: '6-yarder-skip.jpg',
      8: '8-yarder-skip.jpg',
      10: '10-yarder-skip.jpg',
      12: '12-yarder-skip.jpg',
      14: '14-yarder-skip.jpg',
      16: '16-yarder-skip.jpg',
      20: '20-yarder-skip.jpg',
      40: '40-yarder-skip.jpg'
    }

    return `${baseUrl}${imageMap[size] || '16-yarder-skip.jpg'}`
  }

  return (
    <div
      className={`skip-card ${isSelected ? 'selected' : ''}`}
      onClick={onSelect}
    >
      <div className="hexagon">
        <div className="hexagon-content">
          <div className="skip-size-display">
            <span className="size-number">{skip.size}</span>
            <span className="size-label">YARD</span>
          </div>

          <div className="skip-image-hex">
            <img
              src={getSkipImage(skip.size)}
              alt={`${skip.size} Yard Skip`}
              loading="lazy"
            />
          </div>

          <div className="skip-info">
            <div className="skip-period">{skip.hire_period_days} days</div>

            <div className="skip-features">
              {skip.allowed_on_road && (
                <span className="feature-icon" title="Road Placement">🛣️</span>
              )}
              {skip.allows_heavy_waste && (
                <span className="feature-icon" title="Heavy Waste">🏗️</span>
              )}
            </div>

            <div className="skip-price">
              <span className="price-amount">£{calculateTotalPrice().toFixed(2)}</span>
              <span className="price-label">inc. VAT</span>
            </div>

            <div className="select-indicator">
              {isSelected ? '✓ SELECTED' : 'SELECT'}
            </div>
          </div>
        </div>
      </div>


      {isSelected && (
        <div className="selection-details">
          <div className="process-steps">
            <div className="step completed">
              <div className="step-icon">✓</div>
              <div className="step-content">
                <span className="step-title">Skip Size Selected</span>
                <span className="step-detail">{skip.size} Yard Skip</span>
              </div>
            </div>

            <div className="step next">
              <div className="step-icon">📋</div>
              <div className="step-content">
                <span className="step-title">Permit Check</span>
                <span className="step-detail">Verify placement requirements</span>
              </div>
            </div>

            <div className="step pending">
              <div className="step-icon">📅</div>
              <div className="step-content">
                <span className="step-title">Choose Date</span>
                <span className="step-detail">Select delivery date</span>
              </div>
            </div>

            <div className="step pending">
              <div className="step-icon">💳</div>
              <div className="step-content">
                <span className="step-title">Payment</span>
                <span className="step-detail">Secure online payment</span>
              </div>
            </div>
          </div>

          <div className="selection-summary">
            <div className="summary-row">
              <span className="summary-label">{skip.size} Yard Skip</span>
              <span className="summary-value">£{calculateTotalPrice().toFixed(2)}</span>
            </div>
            <div className="summary-row hire-period">
              <span className="summary-detail">{skip.hire_period_days} day hire</span>
            </div>
          </div>

          <div className="action-buttons">
            <button className="back-btn">Back</button>
            <button className="continue-btn">Continue →</button>
          </div>

          <div className="disclaimer">
            <p>Imagery and information shown throughout this website may not reflect the exact shape or size specification, colours may vary, options and/or accessories may be featured at additional cost.</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default SkipCard
